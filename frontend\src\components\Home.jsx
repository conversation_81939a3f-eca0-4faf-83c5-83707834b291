import React from 'react';
import {
  Con<PERSON>er,
  <PERSON>rid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Box,
  Chip
} from '@mui/material';

const Home = () => {
  const blogPosts = [
    {
      id: 1,
      category: 'Travel',
      title: 'Travel the world!!!!!',
      image: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      categoryColor: '#9c27b0'
    },
    {
      id: 2,
      category: 'Art',
      title: 'Art!!!!!!!!!!!!',
      image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      categoryColor: '#9c27b0'
    },
    {
      id: 3,
      category: 'Food',
      title: 'Food is Art!!!',
      image: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      categoryColor: '#9c27b0'
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={4}>
        {blogPosts.map((post) => (
          <Grid item xs={12} sm={6} md={4} key={post.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                boxShadow: 3,
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 6
                }
              }}
            >
              <CardMedia
                component="img"
                height="200"
                image={post.image}
                alt={post.title}
                sx={{ objectFit: 'cover' }}
              />
              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                <Chip
                  label={post.category}
                  size="small"
                  sx={{
                    backgroundColor: post.categoryColor,
                    color: 'white',
                    fontWeight: 'bold',
                    mb: 1
                  }}
                />
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    fontWeight: 'bold',
                    mb: 2,
                    color: '#333'
                  }}
                >
                  {post.title}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      backgroundColor: '#9c27b0',
                      color: 'white',
                      fontWeight: 'bold',
                      textTransform: 'uppercase',
                      fontSize: '0.75rem',
                      '&:hover': {
                        backgroundColor: '#7b1fa2'
                      }
                    }}
                  >
                    Delete
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      backgroundColor: '#9c27b0',
                      color: 'white',
                      fontWeight: 'bold',
                      textTransform: 'uppercase',
                      fontSize: '0.75rem',
                      '&:hover': {
                        backgroundColor: '#7b1fa2'
                      }
                    }}
                  >
                    Update
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default Home;